/* GamePort - Cyberpunk Theme Enhanced (霓虹电竞 - 深紫蓝色系) */
/* 未来科幻风格，深邃神秘的紫蓝霓虹发光效果 */

:root {
  /* 主要颜色变量 - 深紫蓝色系 */
  --primary-color: #B366FF;
  --primary-hover: #9F4FFF;
  --primary-light: #E6D9FF;
  --secondary-color: #D279D2;
  --accent-color: #7F39FB;

  /* 背景颜色 */
  --header-bg-color: #000029;
  --bg-primary: #00000A;
  --bg-secondary: #000033;
  --bg-tertiary: #000040;
  --bg-dark: #000029;

  /* 文字颜色 */
  --text-primary: #B366FF;
  --text-secondary: #D279D2;
  --text-tertiary: #B366FF;
  --text-light: #8A66CC;
  --text-white: #F0E6FF;

  /* 辅助颜色变量 */
  --border-color: #D279D2;
  --border-hover: #E699E6;
  --shadow-color: rgba(179, 102, 255, 0.3);
  --shadow-hover: rgba(179, 102, 255, 0.5);

  /* 状态颜色 */
  --success-color: #66FFB3;
  --warning-color: #FFB366;
  --error-color: #FF6666;
  --info-color: #66B3FF;

  /* 按钮颜色状态 */
  --btn-primary-bg: #B366FF;
  --btn-primary-hover: #9F4FFF;
  --btn-primary-active: #8A33FF;
  --btn-secondary-bg: #D279D2;
  --btn-secondary-hover: #E699E6;

  /* 链接颜色 */
  --link-color: #B366FF;
  --link-hover: #D279D2;
  --link-visited: #9966CC;

  /* 特殊效果颜色 - 霓虹发光效果 */
  --gradient-primary: linear-gradient(135deg, #B366FF 0%, #D279D2 100%);
  --gradient-secondary: linear-gradient(135deg, #000029 0%, #000040 100%);
  --gradient-tertiary: linear-gradient(135deg, #00000A 0%, #000033 100%);
  --neon-glow: 0 0 10px var(--primary-color), 0 0 20px var(--primary-color), 0 0 30px var(--primary-color);
  --neon-glow-secondary: 0 0 8px var(--secondary-color), 0 0 16px var(--secondary-color), 0 0 24px var(--secondary-color);
  
  /* 卡片和容器颜色 */
  --card-bg: #000033;
  --card-border: #D279D2;
  --card-hover: #000040;
  
  /* 输入框颜色 */
  --input-bg: #000029;
  --input-border: #D279D2;
  --input-focus: #B366FF;
  --input-text: #B366FF;
  
  /* 导航颜色 */
  --nav-bg: #000029;
  --nav-item: #B366FF;
  --nav-item-hover: #D279D2;
  --nav-item-active: #E699E6;
  
  /* 滚动条颜色 */
  --scrollbar-track: #000029;
  --scrollbar-thumb: #D279D2;
  --scrollbar-thumb-hover: #E699E6;
  
  /* 分割线颜色 */
  --divider-color: #4D1A4D;
  --divider-light: #661A66;
  
  /* 高亮和选中颜色 */
  --highlight-bg: rgba(179, 102, 255, 0.1);
  --selection-bg: rgba(210, 121, 210, 0.2);
  --selection-text: #F0E6FF;
  
  /* 阴影效果 */
  --shadow-small: 0 2px 4px rgba(179, 102, 255, 0.1);
  --shadow-medium: 0 4px 8px rgba(179, 102, 255, 0.15);
  --shadow-large: 0 8px 16px rgba(179, 102, 255, 0.2);
  
  /* 过渡效果 */
  --transition-fast: 0.15s ease-in-out;
  --transition-normal: 0.3s ease-in-out;
  --transition-slow: 0.5s ease-in-out;
}
